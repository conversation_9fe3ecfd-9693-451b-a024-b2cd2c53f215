
import React, { useState, useEffect } from 'react';
import {
    Elements,
    useStripe,
    useElements,
    CardElement
} from '@stripe/react-stripe-js';
import { doc, updateDoc, addDoc, collection, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/firebase';
import getStripe from '@/lib/stripe';
import AddressInput2 from '@/components/AddressInput2';

interface PaymentFormProps {
    paymentLinkId: string;
    amount: number;
    originalAmount?: number;
    taxAmount?: number;
    taxRate?: string;
    branch?: string;
    description?: string;
    productName?: string;
    disabled?: boolean;
    initialAddress?: any;
    onTipChange?: (tipAmount: number) => void;
    onAddressChange?: (address: any) => void;
    onTaxClear?: () => void;
}

const PaymentFormInner = ({ paymentLinkId, amount, originalAmount, taxAmount, taxRate, branch, description, productName, disabled = false, initialAddress, onTipChange, onAddressChange, onTaxClear }: PaymentFormProps) => {
    const stripe = useStripe();
    const elements = useElements();
    const [processing, setProcessing] = useState(false);
    const [error, setError] = useState<string>('');
    const [clientSecret, setClientSecret] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [customerEmail, setCustomerEmail] = useState('');
    const [customerPhone, setCustomerPhone] = useState('');
    const [wantsToReview, setWantsToReview] = useState<boolean | null>(true); // Auto-select "Yes"
    const [succeeded, setSucceeded] = useState(false);
    const [reviewRequired, setReviewRequired] = useState(false);
    const [reviewLink, setReviewLink] = useState<string>('');
    const [originalPaymentData, setOriginalPaymentData] = useState<any>(null);
    const [tipAmount, setTipAmount] = useState<number>(0);
    const [customTip, setCustomTip] = useState<string>('');
    const [customerAddress, setCustomerAddress] = useState('');
    const [addressCleared, setAddressCleared] = useState(false);
    const [showTax, setShowTax] = useState(false);
    const [hasValidAddress, setHasValidAddress] = useState(false);

    // Notify parent when tip changes
    useEffect(() => {
        onTipChange?.(tipAmount);
    }, [tipAmount]); // Removed onTipChange from dependencies to prevent infinite loop

    // Notify parent of initial address if provided
    useEffect(() => {
        if (initialAddress && onAddressChange) {
            console.log('💡 PaymentForm: Notifying parent of initial address', initialAddress);
            onAddressChange(initialAddress);
        }
        // Set showTax to true if we have initial tax amount and address
        if (initialAddress && taxAmount && taxAmount > 0) {
            setShowTax(true);
            setHasValidAddress(true);
        }
    }, [initialAddress, taxAmount]); // Removed onAddressChange from dependencies to prevent infinite loop

    // Reset showTax when address is cleared
    useEffect(() => {
        if (addressCleared) {
            setShowTax(false);
            setHasValidAddress(false);
        }
    }, [addressCleared]);

    // Show tax when we receive a valid tax amount and have a valid address
    useEffect(() => {
        if (taxAmount && taxAmount > 0 && hasValidAddress && !addressCleared) {
            console.log('💰 PaymentForm: Showing tax - received valid tax amount', {
                taxAmount,
                hasValidAddress,
                addressCleared
            });
            setShowTax(true);
        }
    }, [taxAmount, hasValidAddress, addressCleared]);

    // Helper function to determine if tax should be displayed
    const shouldShowTax = showTax && taxAmount && taxAmount > 0 && hasValidAddress && !addressCleared;

    // Helper function to determine if we should show the form sections below address
    const shouldShowFormSections = () => {
        // Case 1: Address pre-filled from Firebase with original tax (show everything immediately)
        // Note: taxAmount can be 0 for tax-free areas, so we check if it's a number (not null/undefined)
        const case1 = initialAddress && initialAddress.street && (taxAmount !== null && taxAmount !== undefined) && !addressCleared;

        // Case 2: User entered new address and tax has been calculated
        // We check hasValidAddress instead of shouldShowTax since tax might be 0
        const case2 = hasValidAddress && (taxAmount !== null && taxAmount !== undefined) && !addressCleared;

        const result = case1 || case2;

        console.log('🎯 PaymentForm: shouldShowFormSections check', {
            case1: {
                hasInitialAddress: !!initialAddress,
                hasInitialStreet: !!(initialAddress && initialAddress.street),
                hasTaxAmount: (taxAmount !== null && taxAmount !== undefined),
                taxAmountValue: taxAmount,
                notCleared: !addressCleared,
                result: !!case1
            },
            case2: {
                hasValidAddress,
                hasTaxAmount: (taxAmount !== null && taxAmount !== undefined),
                taxAmountValue: taxAmount,
                notCleared: !addressCleared,
                result: !!case2
            },
            finalResult: result
        });

        return result;
    };

    useEffect(() => {
        const initializePayment = async () => {
            try {
                // Get prefill data from Firebase
                const docRef = doc(db, 'paymentLinks', paymentLinkId);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();
                    setOriginalPaymentData(data);

                    if (data.prefillData) {
                        setCustomerName(data.prefillData.name || '');
                        setCustomerEmail(data.prefillData.email || '');
                        setCustomerPhone(data.prefillData.phone || '');
                    }
                    // Get the review link from the payment link data
                    if (data.reviewLink) {
                        setReviewLink(data.reviewLink);
                    }
                }

                const response = await fetch('/api/create-payment-intent', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        paymentLinkId,
                        amount: amount,
                        branch: branch,
                        description: description,
                        productName: productName,
                        tipAmount: tipAmount
                    })
                });

                if (!response.ok) throw new Error('Payment initialization failed');

                const { clientSecret } = await response.json();
                setClientSecret(clientSecret);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Payment error');
            }
        };

        initializePayment();
    }, [paymentLinkId, amount, branch, description, productName, tipAmount]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!stripe || !elements || !clientSecret || disabled) return;

        if (!customerName || !customerEmail) {
            setError('Name and email are required');
            return;
        }

        if (wantsToReview === null) {
            setError('Please answer the review question');
            setReviewRequired(true);
            return;
        }

        setProcessing(true);
        setError('');

        try {
            const cardElement = elements.getElement(CardElement);
            if (!cardElement) throw new Error('Card element not found');

            const paymentResult = await stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card: cardElement,
                    billing_details: {
                        name: customerName,
                        email: customerEmail,
                        phone: customerPhone || undefined,
                    },
                },
            });

            if (paymentResult.error) {
                throw new Error(paymentResult.error.message);
            }

            if (paymentResult.paymentIntent?.status === 'succeeded') {
                // Get payment method details from the payment intent
                const paymentMethodDetails = paymentResult.paymentIntent.payment_method;

                // Extract card details if available
                let cardDetails = {};
                if (typeof paymentMethodDetails === 'object' && paymentMethodDetails !== null) {
                    if ('card' in paymentMethodDetails && paymentMethodDetails.card) {
                        cardDetails = {
                            brand: paymentMethodDetails.card.brand,
                            last4: paymentMethodDetails.card.last4,
                        };
                    }
                }

                // Prepare update data, filtering out undefined values
                const updateData: any = {
                    isPaid: true,
                    paidAt: new Date().toISOString(),
                    customerName,
                    customerEmail,
                    customerPhone: customerPhone || '',
                    customerAddress,
                    paymentMethod: cardDetails,
                    wantsToReview: wantsToReview,
                    // Add tax breakdown to stored data
                    taxInfo: {
                        originalAmount: originalAmount || amount,
                        taxAmount: taxAmount || 0,
                        tipAmount: tipAmount,
                        totalAmount: (originalAmount || 0) + (taxAmount || 0) + tipAmount
                    }
                };

                // Only add reviewLink if it exists and is not undefined
                if (reviewLink) {
                    updateData.reviewLink = reviewLink;
                } else if (originalPaymentData?.reviewLink) {
                    updateData.reviewLink = originalPaymentData.reviewLink;
                }

                // Add other essential fields from original data if they exist
                if (originalPaymentData?.amount) {
                    updateData.amount = originalPaymentData.amount;
                }
                if (originalPaymentData?.productName) {
                    updateData.productName = originalPaymentData.productName;
                }
                if (originalPaymentData?.description) {
                    updateData.description = originalPaymentData.description;
                }
                if (originalPaymentData?.branch) {
                    updateData.branch = originalPaymentData.branch;
                }
                if (originalPaymentData?.bookingDetails) {
                    updateData.bookingDetails = originalPaymentData.bookingDetails;
                }

                // Update Firestore document
                await updateDoc(doc(db, 'paymentLinks', paymentLinkId), updateData);

                // Approximate Stripe fees (2.9% + $0.30)
                const stripeFees = (amount * 0.029) + 0.30;
                const amountAfterFees = amount - stripeFees;

                // Add payout document
                await addDoc(collection(db, 'payments'), {
                    stripePaymentIntentId: paymentResult.paymentIntent.id,
                    customerId: '',
                    amount,
                    originalAmount: originalAmount || amount,
                    taxAmount: taxAmount || 0,
                    amountAfterFees,
                    description,
                    location: branch,
                    address: customerAddress,
                    customerName,
                    customerEmail,
                    customerPhone: customerPhone || '',
                    payoutStatus: 'UNPAID',
                    timestamp: new Date().toISOString(),
                    paymentMethod: 'card',
                    stripeFees,
                    royaltyCalculated: false,
                    metadata: { branch, productName },
                    eventStartTime: null,
                });

                // Always dispatch the paymentSuccess event with review link
                // Add a small delay to ensure Firebase update completes
                setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('paymentSuccess', {
                        detail: {
                            paymentMethod: cardDetails,
                            customerName,
                            customerEmail,
                            customerPhone,
                            customerAddress,
                            wantsToReview,
                            reviewLink: reviewLink || originalPaymentData?.reviewLink || ''
                        }
                    }));
                }, 500);

                setSucceeded(true);
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Payment failed');
            console.error(err);
        } finally {
            setProcessing(false);
        }
    };

    if (!clientSecret) return <div className="p-4">Initializing payment...</div>;

    if (succeeded) {
        return <div className="text-green-600 text-center">Payment successful! Thank you.</div>;
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            {/* Customer Information */}
            <div>
                <label className="block text-gray-700 mb-1">Name</label>
                <input
                    type="text"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="w-full p-2 border rounded text-gray-900 bg-white"
                    required
                />
            </div>
            <div>
                <label className="block text-gray-700 mb-1">Email</label>
                <input
                    type="email"
                    value={customerEmail}
                    onChange={(e) => setCustomerEmail(e.target.value)}
                    className="w-full p-2 border rounded text-gray-900 bg-white"
                    required
                />
            </div>
            <div>
                <label className="block text-gray-700 mb-1">Phone (optional)</label>
                <input
                    type="tel"
                    value={customerPhone}
                    onChange={(e) => setCustomerPhone(e.target.value)}
                    className="w-full p-2 border rounded text-gray-900 bg-white"
                />
            </div>

            {/* Address Input */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 font-helvetica">
                        Billing Address
                    </h3>
                    {initialAddress && !addressCleared && (
                        <button
                            type="button"
                            onClick={() => {
                                console.log('🗑️ PaymentForm: Clear Address button clicked');
                                // Set the cleared flag to force component re-render
                                setAddressCleared(true);
                                // Clear tax display
                                setShowTax(false);
                                setHasValidAddress(false);

                                // Notify parent to clear tax
                                console.log('🗑️ PaymentForm: Calling onTaxClear due to address clear');
                                onTaxClear?.();

                                // Reset to empty address
                                const emptyAddress = {
                                    address: '',
                                    street: '',
                                    city: '',
                                    state: '',
                                    postalCode: ''
                                };
                                console.log('🗑️ PaymentForm: Calling onAddressChange with empty address', emptyAddress);
                                onAddressChange?.(emptyAddress);
                            }}
                            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                            Clear Address
                        </button>
                    )}
                </div>
                <div className="space-y-3">
                    <AddressInput2
                        key={addressCleared ? 'cleared' : (initialAddress ? `${initialAddress.street}-${initialAddress.city}` : 'no-address')}
                        value={!addressCleared && initialAddress ? {
                            street: initialAddress.street || '',
                            city: initialAddress.city || '',
                            state: initialAddress.state || '',
                            postalCode: initialAddress.postalCode || '',
                            country: 'US'
                        } : undefined}
                        onInputChange={() => {
                            console.log('⌨️ PaymentForm: User started typing in address field');
                            // Clear tax display when user starts typing in address field
                            setShowTax(false);
                            setHasValidAddress(false);
                            // Notify parent to clear tax
                            console.log('⌨️ PaymentForm: Calling onTaxClear due to input change');
                            onTaxClear?.();
                        }}
                        onAddressSelected={(selectedAddress) => {
                            console.log('📍 PaymentForm: Address selected from Google Places', {
                                selectedAddress,
                                timestamp: new Date().toISOString()
                            });

                            const formatted = `${selectedAddress.street}, ${selectedAddress.city}, ${selectedAddress.state} ${selectedAddress.postalCode}`;
                            const addressData = {
                                address: formatted,
                                street: selectedAddress.street,
                                city: selectedAddress.city,
                                state: selectedAddress.state,
                                postalCode: selectedAddress.postalCode
                            };

                            console.log('📍 PaymentForm: Formatted address data', addressData);

                            setCustomerAddress(formatted);
                            // Clear tax display when new address is selected
                            setShowTax(false);
                            setHasValidAddress(true); // New address selected, so we have a valid address
                            setAddressCleared(false); // Address is no longer cleared since we just selected one

                            // Notify parent to clear tax initially, they should recalculate based on new address
                            console.log('📍 PaymentForm: Calling onTaxClear due to address selection');
                            onTaxClear?.();

                            console.log('📍 PaymentForm: Calling onAddressChange with new address');
                            onAddressChange?.(addressData);
                        }}
                    />
                </div>
            </div>



            {/* Message when form sections are hidden */}
            {!shouldShowFormSections() && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                    <p className="text-blue-800 text-sm font-medium font-helvetica">
                        📍 Please enter your billing address above to calculate tax and continue with payment
                    </p>
                </div>
            )}

            {/* All form sections below - Only show when form sections should be visible */}
            {shouldShowFormSections() && (
                <>
                {/* Tip Selection */}
                <div className="p-3 rounded-lg border border-gray-200 bg-gray-50">
                    <label className="block mb-2 font-medium text-gray-700">
                        Would you like to add a tip for your detailer?
                    </label>
                <div className="grid grid-cols-4 gap-2 mb-3">
                    {[15, 18, 20, 25].map((percent) => {
                        // Always calculate tip on the original service amount, not including previous tips
                        const baseAmount = originalAmount || amount;
                        const tipValue = Math.round(baseAmount * (percent / 100) * 100) / 100;
                        return (
                            <button
                                key={percent}
                                type="button"
                                onClick={() => {
                                    setTipAmount(tipValue);
                                    setCustomTip('');
                                }}
                                className={`px-3 py-2 rounded text-sm font-medium ${tipAmount === tipValue
                                        ? 'bg-green-600 text-white'
                                        : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                                    }`}
                            >
                                {percent}%<br />${tipValue.toFixed(2)}
                            </button>
                        );
                    })}
                </div>
                <div className="flex gap-2 items-center">
                    <input
                        type="number"
                        placeholder="Enter tip amount or leave blank"
                        value={customTip}
                        onChange={(e) => {
                            setCustomTip(e.target.value);
                            setTipAmount(parseFloat(e.target.value) || 0);
                        }}
                        className="flex-1 p-2 border rounded text-gray-900 bg-white"
                        min="0"
                        step="0.01"
                    />
                    <button
                        type="button"
                        onClick={() => {
                            setTipAmount(0);
                            setCustomTip('');
                        }}
                        className={`px-3 py-2 rounded text-sm font-medium ${tipAmount === 0
                                ? 'bg-gray-600 text-white'
                                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                    >
                        No Tip
                    </button>
                </div>
                </div>

                {/* Price Breakdown */}
                <div className="bg-gray-50 rounded-lg px-4 py-3">
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-gray-600 font-medium font-helvetica">Services Total</span>
                        <span className="text-lg font-semibold text-gray-900 font-helvetica">
                            ${(originalAmount || amount).toFixed(2)}
                        </span>
                    </div>

                    {/* Only show tax section if shouldShowTax is true */}
                    {shouldShowTax ? (
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 font-medium font-helvetica">
                                Sales Tax ({taxRate}%)
                            </span>
                            <span className="text-lg font-semibold text-gray-900 font-helvetica">
                                ${taxAmount.toFixed(2)}
                            </span>
                        </div>
                    ) : (
                        /* Show placeholder when we don't have calculated tax */
                        <div className="flex items-center justify-between">
                            <span className="text-gray-500 font-medium font-helvetica italic">
                                Sales Tax
                            </span>
                            <span className="text-sm text-gray-500 font-helvetica italic">
                                Enter address to calculate
                            </span>
                        </div>
                    )}

                    {tipAmount > 0 ? (
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 font-medium font-helvetica">Tip</span>
                            <span className="text-lg font-semibold text-gray-900 font-helvetica">
                                ${tipAmount.toFixed(2)}
                            </span>
                        </div>
                    ) : null}

                    <div className="border-t border-gray-200 pt-2">
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 font-medium font-helvetica">Total</span>
                            <span className="text-3xl font-bold text-gray-900 font-helvetica">
                                ${(shouldShowTax ? amount + tipAmount : (originalAmount || amount) + tipAmount).toFixed(2)}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Card Details */}
            <div>
                <label className="block text-gray-700 mb-1">Card Details</label>
                <CardElement
                    className="p-2 border rounded"
                    options={{
                        style: {
                            base: {
                                fontSize: '16px',
                                color: '#111827',
                                '::placeholder': { color: '#aab7c4' },
                            },
                            invalid: { color: '#9e2146' },
                        },
                    }}
                />
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            
            {/* Review Question - Above Pay Button */}
            <div className={`p-3 rounded-lg border-2 mb-4 ${reviewRequired ? 'border-red-500 bg-red-50' : 'border-gray-200 bg-gray-50'}`}>
                <label className={`block mb-2 font-medium ${reviewRequired ? 'text-red-700' : 'text-gray-700'}`}>
                    Would you like to leave a review for your detailer? {reviewRequired && <span className="text-red-500">*</span>}
                </label>
                <div className="flex gap-4">
                    <button
                        type="button"
                        onClick={() => {
                            setWantsToReview(true);
                            setReviewRequired(false);
                        }}
                        className={`px-4 py-2 rounded text-sm font-medium ${wantsToReview === true
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                    >
                        Yes
                    </button>
                    <button
                        type="button"
                        onClick={() => {
                            setWantsToReview(false);
                            setReviewRequired(false);
                        }}
                        className={`px-4 py-2 rounded text-sm font-medium ${wantsToReview === false
                            ? 'bg-gray-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                    >
                        No
                    </button>
                </div>

                {reviewRequired && (
                    <div className="mt-2 p-2 bg-red-100 rounded text-sm text-red-700 border border-red-200">
                        Please select whether you'd like to leave a review.
                    </div>
                )}
            </div>

            <button
                type="submit"
                disabled={!stripe || processing || disabled || wantsToReview !== true}
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${!stripe || processing || disabled || wantsToReview !== true
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
            >
                {processing ? 'Processing...' : disabled ? 'Enter Address to Continue' : wantsToReview !== true ? 'Please confirm review preference' : `Pay $${(shouldShowTax ? amount + tipAmount : (originalAmount || amount) + tipAmount).toFixed(2)}`}
            </button>
            <div className="text-center text-sm text-gray-600 mt-3">
                A receipt will automatically be emailed to you.
            </div>
            </>
            )}
        </form>
    );
};

export default function PaymentFormWrapper(props: PaymentFormProps) {
    return (
        <Elements stripe={getStripe()}>
            <PaymentFormInner {...props} />
        </Elements>
    );
}
